<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 ПРОВЕРКА СОСТОЯНИЯ СТАНА\n";
echo "=" . str_repeat("=", 30) . "\n\n";

// Получаем пользователя admin
$user = User::where('name', 'admin')->first();

if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "👤 Пользователь: {$user->name} (ID: {$user->id})\n";
echo "🕐 Текущее время: " . now()->format('Y-m-d H:i:s') . "\n\n";

// Проверяем состояние стана
$isStunned = $user->isStunned();
echo "📊 Состояние стана: " . ($isStunned ? "🔒 ОГЛУШЕН" : "✅ НЕ ОГЛУШЕН") . "\n";

// Проверяем возможность атаки
$canAttack = $user->canAttack();
echo "⚔️ Может атаковать: " . ($canAttack ? "✅ ДА" : "❌ НЕТ") . "\n\n";

// Получаем все активные эффекты
$allEffects = $user->activeEffects()->get();
echo "🧪 Всего активных эффектов: {$allEffects->count()}\n";

if ($allEffects->count() > 0) {
    echo "\n📋 Список всех активных эффектов:\n";
    foreach ($allEffects as $effect) {
        $isActive = $effect->isActive();
        $isStunEffect = $effect->isStunEffect();
        $timeLeft = $effect->ends_at ? $effect->ends_at->diffInSeconds(now(), false) : 'Бессрочно';
        
        $status = '';
        if ($isStunEffect) $status .= ' [СТАН]';
        if (!$isActive) $status .= ' [НЕАКТИВЕН]';
        
        echo "   - ID: {$effect->id}\n";
        echo "     Тип: {$effect->effect_type}{$status}\n";
        echo "     Skill ID: {$effect->skill_id}\n";
        echo "     Начало: {$effect->starts_at}\n";
        echo "     Конец: {$effect->ends_at}\n";
        
        if ($timeLeft !== 'Бессрочно') {
            if ($timeLeft > 0) {
                echo "     Осталось: {$timeLeft} сек.\n";
            } else {
                echo "     Истек: " . abs($timeLeft) . " сек. назад\n";
            }
        }
        
        if (isset($effect->effect_data['message'])) {
            echo "     Сообщение: {$effect->effect_data['message']}\n";
        }
        echo "\n";
    }
} else {
    echo "ℹ️ Активные эффекты отсутствуют.\n";
}

// Проверяем эффекты стана отдельно
$stunEffects = $allEffects->filter(function ($effect) {
    return $effect->isStunEffect();
});

echo "🔒 Эффекты стана: {$stunEffects->count()}\n";

if ($stunEffects->count() > 0) {
    echo "\n📋 Детали эффектов стана:\n";
    foreach ($stunEffects as $effect) {
        $isActive = $effect->isActive();
        echo "   - ID: {$effect->id} " . ($isActive ? "[АКТИВЕН]" : "[НЕАКТИВЕН]") . "\n";
        echo "     Заканчивается: {$effect->ends_at}\n";
        
        if ($effect->ends_at) {
            $timeLeft = $effect->ends_at->diffInSeconds(now(), false);
            if ($timeLeft > 0) {
                echo "     Осталось: {$timeLeft} сек.\n";
            } else {
                echo "     Истек: " . abs($timeLeft) . " сек. назад\n";
            }
        }
        echo "\n";
    }
}

echo "\n🎯 РЕКОМЕНДАЦИИ:\n";

if ($isStunned) {
    echo "🔒 Игрок оглушен. Навигация должна быть заблокирована.\n";
    echo "   - Проверьте в браузере, что кнопки затемнены\n";
    echo "   - Убедитесь, что доступна только кнопка 'Обновить страницу'\n";
    echo "   - Для снятия стана используйте: php clear_stun_effects.php\n";
} else {
    echo "✅ Игрок не оглушен. Навигация должна работать нормально.\n";
    echo "   - Все кнопки должны быть активными\n";
    echo "   - Для тестирования стана используйте: php test_stun_navigation_blocking.php\n";
}

echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";
