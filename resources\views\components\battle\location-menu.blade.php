{{--
Компонент для отображения меню боевых локаций
--}}

@props(['isStunned' => false])

@php
    // Проверяем, заблокированы ли рудники для текущего пользователя
    $locationAccessService = app(\App\Services\LocationAccessService::class);
    $user = Auth::user();
    $isMinesBlocked = $user ? $locationAccessService->isLocationBlocked('Рудники', $user) : false;
@endphp

<div class="px-2 py-1">
    <div class="grid grid-cols-1 rounded-md overflow-hidden border border-[#a6925e] shadow-lg">
        {{-- Заголовок секции --}}
        <div class="bg-gradient-to-r from-[#3b3a33] to-[#4a4a3d] py-1 px-2 border-b border-[#a6925e]">
            <h3 class="text-center text-[#e5b769] font-semibold text-sm tracking-wider uppercase">Боевые локации
            </h3>
        </div>

        {{-- Аванпосты --}}
        <x-battle.location-item route="{{ route('battle.outposts.index') }}" icon="assets/iconOutpost.png"
            title="Аванпосты" :isActive="true" :isStunned="$isStunned" />

        {{-- Рудники --}}
        <x-battle.location-item route="{{ route('battle.mines.index') }}" icon="assets/iconMines.png" title="Рудники"
            :isActive="!$isMinesBlocked" :isBlocked="$isMinesBlocked" :isStunned="$isStunned" />

        {{-- Подземелья --}}
        <x-battle.location-item route="{{ route('dungeons.index') }}" icon="assets/iconDungeon.png" title="Подземелья"
            :isActive="true" :isStunned="$isStunned" />

        {{-- Декоративный разделитель внутри меню --}}
        <div class="px-2 py-0 bg-gradient-to-r from-[#292722] to-[#38352c]">
            <div class="h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50"></div>
        </div>

        {{-- Испытание --}}
        <x-battle.location-item route="#" icon="assets/iconTrial.png" title="Испытание" :isActive="false"
            badge="В разработке" onClick="showMobileTooltip('trial-tooltip')" :isStunned="$isStunned" />

        {{-- Доминионы --}}
        <x-battle.location-item route="#" icon="assets/iconDominions.png" title="Доминионы" :isActive="false"
            badge="В разработке" onClick="showMobileTooltip('dominions-tooltip')" :isStunned="$isStunned" />

        {{-- Временные события --}}
        <x-battle.location-item route="#" icon="assets/iconTemporary.png" title="Временные событие" :isActive="false"
            badge="В разработке" onClick="showMobileTooltip('events-tooltip')" :isStunned="$isStunned" />
    </div>
</div>