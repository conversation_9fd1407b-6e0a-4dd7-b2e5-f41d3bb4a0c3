@props(['botsInLocation' => [], 'user' => null, 'routePrefix' => 'battle.mines.custom', 'isStunned' => false])

{{--
Компонент для отображения списка ботов в локациях рудника
Принимает:
- botsInLocation: коллекция ботов в текущей локации
- user: текущий пользователь
- routePrefix: префикс маршрута для действий
- isStunned: флаг оглушения игрока
--}}

<div class="-mt-4 m-0 p-0" style="margin-top: -17px !important;">
    <div class="flex flex-wrap justify-center gap-2">
        @forelse ($botsInLocation as $bot)
            @php
                $isSelected = $user->current_target_id == $bot->id && $user->current_target_type == 'bot';
                $hpPercent = ($bot->hp / $bot->max_hp) * 100;
                $hpColor = $hpPercent > 70 ? '#4CAF50' : ($hpPercent > 35 ? '#FFC107' : '#F44336');
            @endphp
            <form action="{{ route($routePrefix . '.select-bot', [request()->route('slug'), $bot->id]) }}" method="POST"
                class="m-0 p-0" style="margin: 0; padding: 0;">
                @csrf
                <button type="submit" {{ $isStunned ? 'disabled' : '' }}
                    class="w-16 m-0 p-0 {{ $isSelected ? 'bot-card-glow' : '' }} {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : '' }}"
                    style="margin: 0; padding: 0;">
                    <div class="flex flex-col items-center">
                        {{-- Иконка бота --}}
                        <div class="relative mb-1">
                            @php
                                // Определяем иконку бота на основе расы и класса
                                $botIcon = 'assets/bots/default.png';
                                if ($bot->race === 'solarius' && $bot->class === 'warrior') {
                                    $botIcon = 'assets/bots/sol_warrior.png';
                                } elseif ($bot->race === 'solarius' && $bot->class === 'mage') {
                                    $botIcon = 'assets/bots/sol_mage.png';
                                } elseif ($bot->race === 'lunarius' && $bot->class === 'warrior') {
                                    $botIcon = 'assets/bots/lun_warrior.png';
                                } elseif ($bot->race === 'lunarius' && $bot->class === 'mage') {
                                    $botIcon = 'assets/bots/lun_mage.png';
                                }
                            @endphp
                            <img src="{{ asset($botIcon) }}" alt="{{ $bot->name }}"
                                class="w-10 h-10 rounded-md {{ $isSelected ? 'bot-icon-glow' : '' }}">

                            {{-- Индикатор уровня бота --}}
                            <div
                                class="absolute -bottom-1 -right-1 bg-[#3b3a33] text-[#e9d5a0] text-[8px] rounded-full w-3.5 h-3.5 flex items-center justify-center border border-[#8c7a55]">
                                {{ $bot->level }}
                            </div>
                        </div>

                        {{-- Имя бота --}}
                        <span
                            class="text-[#e9d5a0] text-[9px] font-semibold truncate w-full text-center">{{ $bot->name }}</span>

                        {{-- Полоска здоровья --}}
                        <div class="w-full bg-[#2a2a2a] rounded-full h-1 mt-1">
                            <div class="h-1 rounded-full transition-all duration-300"
                                style="width: {{ $hpPercent }}%; background-color: {{ $hpColor }};"></div>
                        </div>

                        {{-- Текст HP --}}
                        <span class="text-[#a6925e] text-[7px] mt-0.5">{{ $bot->hp }}/{{ $bot->max_hp }}</span>
                    </div>
                </button>
            </form>
        @empty
            <div class="text-center text-[#a6925e] text-sm py-4">
                В этой локации нет ботов
            </div>
        @endforelse
    </div>
</div>

{{-- Стили для эффектов свечения --}}
<style>
    .bot-card-glow {
        box-shadow: 0 0 10px rgba(74, 147, 98, 0.8);
        border: 2px solid #4a9362;
        border-radius: 8px;
    }

    .bot-icon-glow {
        box-shadow: 0 0 8px rgba(74, 147, 98, 0.6);
        border: 1px solid #4a9362;
    }
</style>