@props([
    'route' => '#',
    'icon' => '',
    'title' => '',
    'isActive' => true,
    'isBlocked' => false,
    'badge' => null,
    'tooltipId' => null,
    'onClick' => null,
    'isStunned' => false
])

{{--
Компонент для отображения элемента боевой локации
Принимает:
- route: маршрут для перехода
- icon: путь к иконке
- title: название локации
- isActive: активна ли локация (по умолчанию true)
- isBlocked: заблокирована ли локация (по умолчанию false)
- badge: текст бейджа (например, "В разработке")
- tooltipId: ID для всплывающей подсказки
- onClick: JavaScript функция для клика (для неактивных элементов)
--}}

@if($isBlocked)
    {{-- Заблокированная локация --}}
    <div class="relative flex items-center text-left text-[#a09a8a] pl-0 pr-2 bg-gradient-to-r from-[#292722] to-[#38352c] border-b border-[#a6925e] overflow-hidden">
        <span
            class="w-10 h-10 flex items-center justify-center mr-0 text-[#a09a8a] bg-gradient-to-br from-[#38352c] to-[#292722] border-r border-[#a6925e] relative">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter grayscale opacity-50">
            {{-- Иконка блокировки --}}
           
        </span>
        <span class="font-medium tracking-wide drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] ml-2 opacity-75">{{ $title }}</span>

        {{-- Статус "На доработке" --}}
        <span class="ml-auto flex items-center space-x-1">
            <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            <span class="text-orange-400 text-xs font-medium">На доработке</span>
        </span>

        {{-- Полупрозрачный оверлей --}}
        <div class="absolute inset-0 bg-black bg-opacity-20 pointer-events-none"></div>

        {{-- Диагональные полосы --}}
        <div class="absolute inset-0 opacity-10 pointer-events-none"
             style="background-image: repeating-linear-gradient(
                 45deg,
                 transparent,
                 transparent 8px,
                 rgba(255, 165, 0, 0.3) 8px,
                 rgba(255, 165, 0, 0.3) 16px
             );">
        </div>
    </div>
@elseif($isActive)
    <a href="{{ $isStunned ? '#' : $route }}"
        class="flex items-center text-left text-[#e0d0a0] pl-0 pr-2 bg-gradient-to-r from-[#292722] to-[#38352c] border-b border-[#a6925e] transition duration-300 hover:from-[#38352c] hover:to-[#4a452c] hover:text-[#e5b769] {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed pointer-events-none' : '' }}"
        {{ $isStunned ? 'onclick="event.preventDefault(); return false;"' : '' }}>
        <span
            class="w-10 h-10 flex items-center justify-center mr-0 text-[#e5b769] bg-gradient-to-br from-[#38352c] to-[#292722] border-r border-[#a6925e]">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter drop-shadow-[0_0_2px_rgba(229,183,105,0.5)]">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] ml-2">{{ $title }}</span>
    </a>
@else
    <div class="flex items-center text-left text-[#a09a8a] pl-0 pr-2 bg-gradient-to-r from-[#292722] to-[#38352c] border-b border-[#a6925e] relative group md:cursor-default cursor-pointer"
        @if($onClick) onclick="{{ $onClick }}" @endif>
        <span
            class="w-10 h-10 flex items-center justify-center mr-0 text-[#a09a8a] bg-gradient-to-br from-[#38352c] to-[#292722] border-r border-[#a6925e]">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter grayscale opacity-70">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] ml-2">{{ $title }}</span>
        
        @if($badge)
            <span class="ml-auto">
                <span
                    class="inline-flex items-center px-1.5 py-0.5 rounded-sm bg-[#3b3a33] text-[#a09a8a] text-xs border border-[#a6925e] relative overflow-hidden">
                    <span class="relative z-10">{{ $badge }}</span>
                    <span
                        class="absolute inset-0 bg-gradient-to-r from-[#3b3a33] via-[#4a4a3d] to-[#3b3a33] opacity-50 animate-pulse"></span>
                </span>
            </span>
        @endif
    </div>
@endif
