🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 2
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-06-21 13:57:08
        Location: Профиль (test)
      - User ID: 10
        Activity: 2025-06-21 13:54:09
        Location: Боевые локации
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 2 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Профиль (test)
      - Тестовый Солариус: Боевые локации
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 2
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 2 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 2 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 10
        Activity: 2025-06-21 14:24:09
        Location: Боевые локации
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Боевые локации
      - Тестовый Солариус: Профиль (test)
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 1 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:10
   ❌ Ошибка синхронизации пользователя ID:7
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 2
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 2
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-06-21 14:53:08
        Location: Профиль (test)
      - User ID: 10
        Activity: 2025-06-21 14:54:09
        Location: Боевые локации
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 2 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Профиль (test)
      - Тестовый Солариус: Боевые локации
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 2
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 2 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 2 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 2
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-06-21 15:23:56
        Location: Кто онлайн
      - User ID: 10
        Activity: 2025-06-21 15:20:16
        Location: Город
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 2 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Кто онлайн
      - Тестовый Солариус: Город
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 2
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 2 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 2 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 2
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-06-21 15:52:07
        Location: Магазин (Выбор)
      - User ID: 10
        Activity: 2025-06-21 15:54:23
        Location: Кто онлайн
   📋 Всего ключей активности: 3
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 2 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Магазин (Выбор)
      - Тестовый Солариус: Кто онлайн
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 2
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 2 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 2 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Магазин (Выбор)
      - Тестовый Солариус: Кто онлайн
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:7
   ❌ Ошибка синхронизации пользователя ID:10
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 2
   📊 После синхронизации:
      - Redis: 0 пользователей
      - OnlineStatusService: 0 пользователей
   ⚠️  Синхронизация частично восстановлена

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
⚠️ Sync fix: warning

Успешных тестов: 4/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Магазин (Выбор)
      - Тестовый Солариус: Кто онлайн
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:7
   ❌ Ошибка синхронизации пользователя ID:10
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 2
   📊 После синхронизации:
      - Redis: 0 пользователей
      - OnlineStatusService: 0 пользователей
   ⚠️  Синхронизация частично восстановлена

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
⚠️ Sync fix: warning

Успешных тестов: 4/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Кто онлайн
      - Тестовый Солариус: Магазин (Выбор)
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:10
   ❌ Ошибка синхронизации пользователя ID:7
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 2
   📊 После синхронизации:
      - Redis: 0 пользователей
      - OnlineStatusService: 0 пользователей
   ⚠️  Синхронизация частично восстановлена

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
⚠️ Sync fix: warning

Успешных тестов: 4/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Кто онлайн
      - Тестовый Солариус: Магазин (Выбор)
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:10
   ❌ Ошибка синхронизации пользователя ID:7
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 2
   📊 После синхронизации:
      - Redis: 0 пользователей
      - OnlineStatusService: 0 пользователей
   ⚠️  Синхронизация частично восстановлена

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
⚠️ Sync fix: warning

Успешных тестов: 4/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Кто онлайн
      - Тестовый Солариус: Магазин (Выбор)
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:10
   ❌ Ошибка синхронизации пользователя ID:7
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 2
   📊 После синхронизации:
      - Redis: 0 пользователей
      - OnlineStatusService: 0 пользователей
   ⚠️  Синхронизация частично восстановлена

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
⚠️ Sync fix: warning

Успешных тестов: 4/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-06-21 18:59:09
        Location: Город
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Город
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 1 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Профиль (test)
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:7
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 1
   📊 После синхронизации:
      - Redis: 0 пользователей
      - OnlineStatusService: 0 пользователей
   ⚠️  Синхронизация частично восстановлена

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
⚠️ Sync fix: warning

Успешных тестов: 4/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Профиль (test)
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:7
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 1
   📊 После синхронизации:
      - Redis: 0 пользователей
      - OnlineStatusService: 0 пользователей
   ⚠️  Синхронизация частично восстановлена

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
⚠️ Sync fix: warning

Успешных тестов: 4/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-06-24 09:49:54
        Location: test
   📋 Всего ключей активности: 3
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 3 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 3 пользователей
   📊 UserController: 3 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: test
      - Тестовый Солариус: test
      - Тестовый Лунариус: Эльфийская Гавань
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 1 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 3
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ❌ Ошибка синхронизации пользователя ID:7
   ❌ Ошибка синхронизации пользователя ID:16
   ❌ Ошибка синхронизации пользователя ID:9
   📊 Синхронизировано: 0 пользователей
   ⚠️  Ошибок: 3
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-06-24 10:15:36
        Location: цуцу
   📋 Всего ключей активности: 3
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: цуцу
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 1 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 08:58:49
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 09:30:09
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 09:59:39
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 11:00:03
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 11:59:09
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 12:25:01
        Location: Тест ботов
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Тест ботов
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 12:59:31
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-17 13:27:40
        Location: аааааааааааа
   📋 Всего ключей активности: 2
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 2 пользователей
   📊 OnlineStatusService: 2 пользователей
   📊 База данных (15 мин): 2 пользователей
   📊 UserController: 2 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
      - ID:1: Аванпосты
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 2
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 2 пользователей
   🗑️  Очищено: 2 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 2
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Сражение (Выбор)
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: Сражение (Выбор)
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: бббббббббббббббб
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: бббббббббббббббб
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: бббббббббббббббб
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: бббббббббббббббб
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: бббббббббббббббб
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: бббббббббббббббб
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-19 20:20:31
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-19 21:29:23
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-21 14:57:56
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-21 15:30:07
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-21 18:29:45
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-21 19:24:36
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-21 20:29:36
        Location: xxxxxxxxxxxxxxxxxx
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: xxxxxxxxxxxxxxxxxx
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: xxxxxxxxxxxxxxxxxx
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-22 13:01:33
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Тарнмор
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: Тарнмор
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-22 15:02:11
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-22 22:02:25
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: тест мобов руд
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: тест мобов руд
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-23 15:30:52
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-23 16:00:01
        Location: Город
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Город
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 0
   🔍 Детальная диагностика Redis:
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 0 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ❌ Счетчики НЕ синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 0
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 0 пользователей
   🗑️  Очищено: 0 пользователей
   📊 После очистки: 0 пользователей
   ℹ️  Неактивных пользователей не найдено
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно
🔧 Тест 6: Исправление синхронизации
   ✅ Синхронизирован пользователь ID:7 в локации: аааааааааааа
   📊 Синхронизировано: 1 пользователей
   📊 После синхронизации:
      - Redis: 1 пользователей
      - OnlineStatusService: 1 пользователей
   ✅ Синхронизация восстановлена!

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
⚠️ Count comparison: warning
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success
✅ Sync fix: success

Успешных тестов: 5/6
⚠️  Обнаружены проблемы в системе онлайн статуса. Требуется внимание.

💡 РЕКОМЕНДАЦИИ:
- Проверьте синхронизацию между Redis и БД
- Убедитесь, что все сервисы используют одинаковый порог активности (15 минут)
- Выполните очистку неактивных пользователей: php artisan test:online-status-sync
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-23 16:59:48
        Location: аааааааааааа
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: аааааааааааа
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
🔍 Начинаем тестирование синхронизации онлайн статуса...

📡 Тест 1: Проверка подключения к Redis
   ✅ Redis подключение: OK (1)
   📊 Пользователей в Redis: 1
   🔍 Детальная диагностика Redis:
      - User ID: 7
        Activity: 2025-07-23 17:28:20
        Location: Аванпосты
   📋 Всего ключей активности: 1
🔢 Тест 2: Сравнение методов подсчета онлайн пользователей
   📊 RedisLocationService: 1 пользователей
   📊 OnlineStatusService: 1 пользователей
   📊 База данных (15 мин): 1 пользователей
   📊 UserController: 1 пользователей
   ✅ Счетчики синхронизированы!
   📋 Детали пользователей из Redis:
      - ID:0: Аванпосты
⏰ Тест 3: Проверка активности пользователей
   ✅ Активных пользователей (≤15 мин): 1
   ⚠️  Неактивных пользователей (>15 мин): 0
   ❓ Пользователей без данных активности: 0
🧹 Тест 4: Очистка неактивных пользователей
   📊 До очистки: 1 пользователей
   🗑️  Очищено: 1 пользователей
   📊 После очистки: 0 пользователей
   ✅ Очистка выполнена успешно
💾 Тест 5: Проверка fallback на базу данных
   📊 Пользователей в БД (fallback): 1
   ✅ Fallback на БД работает корректно

📋 ИТОГОВЫЙ ОТЧЕТ
==================
✅ Redis connection: success
✅ Count comparison: success
✅ Activity check: success
✅ Cleanup test: success
✅ Fallback test: success

Успешных тестов: 5/5
🎉 Все тесты пройдены успешно! Система онлайн статуса работает корректно.
