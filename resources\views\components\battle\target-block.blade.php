@props([
    'target' => null,
    'isStunned' => false,
    'lastAttacker' => null,
    'lastAttackerResources' => null,
    'routePrefix' => null,
    'dungeonId' => null,
])

@php
    // Определяем префикс маршрута на основе текущей локации, если не передан
    if (!$routePrefix) {
        $currentLocation = Auth::user()->statistics->current_location ?? 'Неизвестно';

        // Маппинг локаций к префиксам маршрутов
        $locationRouteMap = [
            'Эльфийская Гавань' => 'battle.outposts.elven_haven',
            'Форт Рассвета' => 'battle.outposts.dawn_fort',
            'Песчаный Оплот' => 'battle.outposts.sandy_stronghold',
            'Аванпосты' => 'battle.outposts',
        ];

        $routePrefix = $locationRouteMap[$currentLocation] ?? 'battle.outposts.elven_haven';
    }

    // Проверяем, находится ли игрок-цель в той же локации, что и текущий пользователь
    $isTargetInLocation = false;
    $currentLocation = Auth::user()->statistics->current_location ?? null;

    if ($target && Auth::user()->current_target_type === 'player') {
        // Получаем актуальные ресурсы игрока-цели из Redis
        $targetActualResources = null;
        try {
            $targetActualResources = $target->profile->getActualResources();
        } catch (\Exception $e) {
            // Если не удается получить актуальные ресурсы, используем данные из БД
            $targetActualResources = [
                'current_hp' => $target->profile->hp ?? 0,
                'max_hp' => $target->profile->max_hp ?? 1
            ];
        }

        // Проверяем, находится ли игрок-цель в той же локации и имеет ли актуальное HP > 0
        $isTargetInLocation = $target->statistics &&
                             $target->statistics->current_location === $currentLocation &&
                             $target->profile &&
                             $targetActualResources['current_hp'] > 0 && // Используем актуальное HP из Redis
                             $target->updated_at >= now()->subMinutes(15); // Проверка онлайн-статуса
    }
@endphp

@php
    // Определяем какое изображение использовать в зависимости от наличия цели
    // Если нет цели (показывается кнопка "Бить любого") - используем action-non-target.png
    // Если есть цель - используем action-bg.png
    $currentTargetType = Auth::user()->current_target_type;
    $hasActiveTarget = !empty($currentTargetType) && 
                       in_array($currentTargetType, ['mob', 'player', 'bot', 'ally']) && 
                       $target !== null;
    $backgroundImage = $hasActiveTarget ? 'assets/UI/action-bg.png' : 'assets/UI/action-non-target.png';
@endphp

<div
    class="relative bg-contain bg-center bg-no-repeat max-w-xs mx-auto"
    style="background-image: url('{{ asset($backgroundImage) }}'); min-height: 120px; background-size: 100% 100%;">
    {{-- Заголовок блока --}}
    <div
        class="text-center py-4 text-[#e9d5a0] font-bold text-sm relative z-10">
        Действия
    </div>

    <div class="p-4 space-y-2 relative z-10">
        @if ($isStunned)
            <div class="bg-yellow-900/50 text-yellow-400 p-4 rounded-lg text-center my-4">
                ⚡ Вы оглушены и не можете действовать! ⚡
            </div>
            {{-- Кнопка обновления страницы при стане --}}
            <button
                onclick="window.location.reload()"
                class="w-full py-2 bg-gradient-to-b from-[#2f473c] to-[#1e2e27]
                border-2 border-[#3b3629] rounded-md text-[#f8eac2] text-sm font-semibold shadow-md
                hover:from-[#3a5647] hover:to-[#253831] active:from-[#253831] active:to-[#1a251f] transition-all
                text-center">
                🔄 Обновить страницу
            </button>
        @else
            {{-- Универсальный блок действий с целью (моб, игрок, бот, союзник) --}}
            @if (in_array(Auth::user()->current_target_type, ['mob', 'player', 'bot', 'ally']) && $target)
                <!--
                    Русский комментарий:
                    Этот блок отображает подробную информацию о текущей цели (моб, игрок или бот).
                    Включает иконку, имя, HP, эффекты, кнопку атаки и прогресс-бар.
                    Для разных типов целей меняется только маршрут формы и скрытые поля.
                -->
                <div class="space-y-1.5">
                    {{-- Блок иконки и информации о цели --}}
                    <div
                        class="flex items-center bg-black/40 p-1.5 rounded-md backdrop-blur-sm">
                        {{-- Иконка цели --}}
                        @if (Auth::user()->current_target_type === 'mob')
                            <img src="{{ asset($target->icon ?? 'wolfIcon.png') }}" alt="{{ $target->name }}"
                                class="w-6 h-6 rounded-md">
                        @elseif(Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot')
                            <div
                                class="w-6 h-6 rounded-md bg-gradient-to-b from-[#913838] to-[#762323] border border-[#c07777] flex items-center justify-center text-white font-bold text-xs">
                                {{ Auth::user()->current_target_type === 'player' ? 'PvP' : 'PvP' }}
                            </div>
                        @elseif(Auth::user()->current_target_type === 'ally')
                            <div
                                class="w-6 h-6 rounded-md bg-gradient-to-b from-[#3e5c48] to-[#243c2f] border border-[#6c8a6c] flex items-center justify-center text-white font-bold text-xs">
                                👥
                            </div>
                        @endif
                        <div class="ml-1.5 flex-1">
                            {{-- Имя и HP цели --}}
                            <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                {{ $target->name }}
                                <span
                                    class="text-[10px] {{ Auth::user()->current_target_type === 'mob' ? 'text-[#9c8d69]' : 'text-red-400' }}">
                                    {{--
                                        Русский комментарий:
                                        Отображаем HP и максимум для цели. Для моба — свои поля, для игрока используем
                                        метод getActualResources(), который рассчитывает актуальное HP на лету.
                                    --}}
                                    @if (Auth::user()->current_target_type === 'mob')
                                        {{ $target->hp }}/{{ $target->max_hp }}
                                    @elseif(Auth::user()->current_target_type === 'player')
                                        @php
                                            // Используем try-catch для корректной обработки возможных ошибок
                                            try {
                                                // Получаем актуальные данные о HP из Redis напрямую через PlayerHealthService
                                                $playerHealthService = app(\App\Services\PlayerHealthService::class);
                                                $currentHP = $playerHealthService->getCurrentHP($target);
                                                $targetActualResources = [
                                                    'current_hp' => $currentHP,
                                                    'current_mp' => $target->profile->current_mp ?? 0,
                                                ];
                                            } catch (\Exception $e) {
                                                // При ошибке используем значения по умолчанию
                                                $targetActualResources = [
                                                    'current_hp' =>
                                                        $target->profile->current_hp ?? ($target->profile->hp ?? 0),
                                                    'current_mp' =>
                                                        $target->profile->current_mp ?? ($target->profile->mp ?? 0),
                                                ];
                                                \Illuminate\Support\Facades\Log::error(
                                                    'Ошибка получения актуальных ресурсов цели: ' . $e->getMessage(),
                                                    [
                                                        'target_id' => $target->id,
                                                        'profile_exists' => isset($target->profile),
                                                    ],
                                                );
                                            }
                                        @endphp
                                        ❤️
                                        {{ $targetActualResources['current_hp'] }}/{{ $target->profile->max_hp ?? 100 }}
                                    @elseif(Auth::user()->current_target_type === 'bot')
                                        @php
                                            // Используем try-catch для обработки возможных ошибок
                                            try {
                                                // Используем метод getActualResources для получения актуального HP
                                                $botActualResources = $target->getActualResources();
                                            } catch (\Exception $e) {
                                                // Если произошла ошибка, используем значения по умолчанию
                                                $botActualResources = [
                                                    'current_hp' => $target->hp ?? 0,
                                                    'current_mp' => $target->mp ?? 0,
                                                ];
                                                \Illuminate\Support\Facades\Log::error(
                                                    'Ошибка получения актуальных ресурсов бота: ' . $e->getMessage(),
                                                );
                                            }
                                        @endphp
                                        ❤️
                                        {{ $botActualResources['current_hp'] }}/{{ $target->max_hp ?? 100 }}
                                    @elseif(Auth::user()->current_target_type === 'ally')
                                        @php
                                            // Получаем актуальные ресурсы союзника
                                            try {
                                                $playerHealthService = app(\App\Services\PlayerHealthService::class);
                                                $currentHP = $playerHealthService->getCurrentHP($target);
                                                $allyActualResources = [
                                                    'current_hp' => $currentHP,
                                                    'current_mp' => $target->profile->current_mp ?? 0,
                                                ];
                                            } catch (\Exception $e) {
                                                // При ошибке используем значения по умолчанию
                                                $allyActualResources = [
                                                    'current_hp' => $target->profile->current_hp ?? ($target->profile->hp ?? 0),
                                                    'current_mp' => $target->profile->current_mp ?? ($target->profile->mp ?? 0),
                                                ];
                                                \Illuminate\Support\Facades\Log::error(
                                                    'Ошибка получения актуальных ресурсов союзника: ' . $e->getMessage(),
                                                    ['target_id' => $target->id, 'profile_exists' => isset($target->profile)]
                                                );
                                            }
                                        @endphp
                                        👥
                                        {{ $allyActualResources['current_hp'] }}/{{ $target->profile->max_hp ?? 100 }}
                                    @endif
                                </span>
                            </div>
                            {{-- Полоска HP цели --}}
                            <div
                                class="flex items-center {{ Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot' || Auth::user()->current_target_type === 'ally' ? 'mt-0.5' : '' }}">
                                <div class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                    <div class="h-full"
                                        style="width:
                                            @if (Auth::user()->current_target_type === 'mob') {{ ($target->hp / $target->max_hp) * 100 }}%
                                            @elseif(Auth::user()->current_target_type === 'player')
                                                {{ ($targetActualResources['current_hp'] / ($target->profile->max_hp ?? 100)) * 100 }}%
                                            @elseif(Auth::user()->current_target_type === 'bot')
                                                {{ ($botActualResources['current_hp'] / ($target->max_hp ?? 100)) * 100 }}%
                                            @elseif(Auth::user()->current_target_type === 'ally')
                                                {{ ($allyActualResources['current_hp'] / ($target->profile->max_hp ?? 100)) * 100 }}% @endif
                                        ; background-color:
                                            @php
$hp = 0;
                                                $max = 1;
                                                if (Auth::user()->current_target_type === 'mob') {
                                                    $hp = $target->hp;
                                                    $max = $target->max_hp;
                                                } elseif (Auth::user()->current_target_type === 'player') {
                                                    $hp = $targetActualResources['current_hp'];
                                                    $max = $target->profile->max_hp ?? 100;
                                                } elseif (Auth::user()->current_target_type === 'bot') {
                                                    $hp = $botActualResources['current_hp'];
                                                    $max = $target->max_hp ?? 100;
                                                } elseif (Auth::user()->current_target_type === 'ally') {
                                                    $hp = $allyActualResources['current_hp'];
                                                    $max = $target->profile->max_hp ?? 100;
                                                } @endphp
                                            {{ $hp > $max * 0.7 ? '#4CAF50' : ($hp > $max * 0.3 ? '#FFC107' : '#F44336') }};">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{-- Кнопка атаки цели (НЕ показывается для союзников) --}}
                    @if (Auth::user()->current_target_type !== 'ally')
                        <form id="attackForm"
                            action="@if ($routePrefix === 'dungeons')
                                {{ route('dungeons.attack', $dungeonId) }}
                            @elseif (Auth::user()->current_target_type === 'mob')
                                {{ route($routePrefix . '.attack', ['id' => request()->route('id')]) }}
                            @else
                                {{ route($routePrefix . '.attack_any_player', ['id' => request()->route('id')]) }}
                            @endif"
                            method="POST">
                            @csrf
                            {{-- Для бота и игрока нужны скрытые поля --}}
                            @if (Auth::user()->current_target_type === 'bot')
                                <input type="hidden" name="target_type" value="bot">
                                <input type="hidden" name="target_id" value="{{ $target->id }}">
                            @elseif(Auth::user()->current_target_type === 'player')
                                <input type="hidden" name="target_type" value="user">
                                <input type="hidden" name="target_id" value="{{ $target->id }}">
                            @endif
                            <button id="attackButton" type="submit"
                                class="w-full py-2
                                    @if (Auth::user()->current_target_type === 'mob') bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777]
                                    @else
                                        bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777] @endif
                                    rounded-md text-white text-sm font-bold shadow-md
                                    hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e] transition-all text-center">
                                {{--
                                    Русский комментарий:
                                    Текст кнопки одинаков для всех типов целей
                                --}}
                                @if (Auth::user()->current_target_type === 'mob')
                                    Атаковать
                                @elseif(Auth::user()->current_target_type === 'player')
                                    Атаковать
                                @else
                                    Атаковать
                                @endif
                            </button>
                            {{-- Полоса заряда атаки --}}
                            <div class="w-full h-2 bg-[#222019] rounded-sm mt-1.5 border border-[#46423a] overflow-hidden">
                                <div id="progressBar" class="h-full bg-[#F44336] rounded-sm transition-all"
                                    style="width: 0%;"></div>
                            </div>
                        </form>
                    @else
                        {{-- Для союзников показываем информационное сообщение --}}
                        <div class="w-full py-3 bg-gradient-to-b from-[#3e5c48] to-[#243c2f] border-2 border-[#6c8a6c]
                                   rounded-md text-[#fceac4] text-sm font-semibold text-center">
                            👥 Союзник выбран для применения навыков
                        </div>
                    @endif
                    {{-- Кнопка смены цели (только для аванпостов, не для подземелий) --}}
                    @if ($routePrefix !== 'dungeons' && (Auth::user()->current_target_type === 'mob' || Auth::user()->current_target_type === 'bot' || Auth::user()->current_target_type === 'player'))
                        <form action="{{ route($routePrefix . '.change_target', ['id' => request()->route('id')]) }}" method="POST"
                              onsubmit="return handleChangeTargetSubmit(this)">
                            @csrf
                            <button type="submit"
                                class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b] transition-all text-center change-target-btn">
                                <span class="button-text">Сменить цель</span>
                            </button>
                        </form>
                    @endif
                </div>
            @else
                {{-- Нет цели - показываем заголовок блока и кнопку выбора случайного игрока (только для аванпостов) --}}
                @if ($routePrefix !== 'dungeons')
                    <form action="{{ route($routePrefix . '.attack_any_player', ['id' => request()->route('id')]) }}" method="POST">
                        @csrf
                        <button type="submit"
                            class="w-full py-2 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b] transition-all text-center">
                            Бить любого
                        </button>
                    </form>
                @else
                    {{-- Для подземелий показываем сообщение о необходимости выбрать цель --}}
                    <div class="text-center py-4">
                        <p class="text-[#a6925e] text-sm">Выберите моба или союзника для взаимодействия</p>
                    </div>
                @endif
            @endif

            {{-- Дополнительные кнопки (Ответный удар) --}}
            <div class="pt-1 space-y-1.5">
                @php
                    // Получаем текущего пользователя и его локацию
                    $currentUser = Auth::user();
                    $currentLocation = $currentUser->statistics->current_location ?? 'Неизвестно';

                    // Проверяем последнего атакующего с учетом активности и местоположения
                    $validLastAttacker = null;
                    $validLastAttackerResources = null;

                    if ($currentUser->last_attacker_id) {
                        $validLastAttacker = \App\Models\User::where('id', $currentUser->last_attacker_id)
                            ->whereHas('statistics', function ($query) use ($currentLocation) {
                                $query->where('current_location', $currentLocation);
                            })
                            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp) // Проверка активности 5 минут
                            ->with('profile', 'statistics')
                            ->first();

                        // Получаем ресурсы атакующего если он найден
                        if ($validLastAttacker) {
                            $validLastAttackerResources = $validLastAttacker->profile->getActualResources();
                        }
                    }

                    // Проверяем, не выбран ли уже атакующий в качестве цели
                    $isAttackerAlreadyTargeted = $validLastAttacker &&
                        $currentUser->current_target_type === 'player' &&
                        $currentUser->current_target_id == $validLastAttacker->id;
                @endphp

                @if ($routePrefix !== 'dungeons' && $validLastAttacker && $validLastAttackerResources && $validLastAttackerResources['current_hp'] > 0 && !$isAttackerAlreadyTargeted)
                    <form action="{{ route($routePrefix . '.retaliate', ['id' => request()->route('id')]) }}" method="POST">
                        @csrf
                        <button type="submit"
                            class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323]
                                border-2 border-[#c07777] rounded-md text-white text-xs font-semibold shadow-md
                                hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]
                                transition-all duration-300 text-center group relative overflow-hidden">

                            {{-- Фоновая анимация при наведении --}}
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b6b40] to-transparent opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>

                            <div class="flex items-center justify-center relative z-10">
                                {{-- Иконка атаки --}}
                                <span class="mr-1 text-red-300">⚔️</span>

                                {{-- Текст кнопки --}}
                                <span class="font-bold">Бить в ответ</span>

                                {{-- Имя и HP противника в стильном контейнере --}}
                                <div
                                    class="ml-2 flex items-center bg-[#6b2c2c] px-2 py-0.5 rounded border border-[#c07777] shadow-inner">
                                    <span class="text-[10px] text-yellow-200 mr-1">{{ $validLastAttacker->name }}</span>

                                    {{-- Мини-полоска HP --}}
                                    <div class="w-14 h-1.5 bg-[#421a1a] rounded-full overflow-hidden flex-shrink-0">
                                        <div class="{{ $validLastAttackerResources['current_hp'] > $validLastAttacker->profile->max_hp * 0.7 ? 'bg-green-600' : ($validLastAttackerResources['current_hp'] > $validLastAttacker->profile->max_hp * 0.3 ? 'bg-yellow-500' : 'bg-red-600') }} h-full"
                                            style="width: {{ ($validLastAttackerResources['current_hp'] / $validLastAttacker->profile->max_hp) * 100 }}%;">
                                        </div>
                                    </div>

                                    {{-- Числовое значение HP --}}
                                    <span class="text-[9px] text-gray-300 ml-1">
                                        {{ $validLastAttackerResources['current_hp'] }}<span
                                            class="text-gray-500">/{{ $validLastAttacker->profile->max_hp }}</span>
                                    </span>
                                </div>
                            </div>
                        </button>
                    </form>
                @endif
            </div>
        @endif
    </div>
</div>

{{-- JavaScript для debouncing кнопки "Сменить цель" --}}
<script>
// Проверяем, не определена ли уже функция
if (typeof handleChangeTargetSubmit === 'undefined') {
    let lastChangeTargetTime = 0;
    const CHANGE_TARGET_COOLDOWN = 1000; // 1 секунда

    function handleChangeTargetSubmit(form) {
        const currentTime = Date.now();
        const timeSinceLastChange = currentTime - lastChangeTargetTime;

        if (timeSinceLastChange < CHANGE_TARGET_COOLDOWN) {
            const remainingTime = Math.ceil((CHANGE_TARGET_COOLDOWN - timeSinceLastChange) / 1000);

            // Показываем сообщение об ошибке
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg z-50';
            errorDiv.textContent = `Подождите ${remainingTime} сек. перед сменой цели`;
            document.body.appendChild(errorDiv);

            // Удаляем сообщение через 3 секунды
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 3000);

            return false; // Предотвращаем отправку формы
        }

        // Обновляем время последней смены цели
        lastChangeTargetTime = currentTime;

        // Блокируем кнопку и показываем индикатор загрузки
        const button = form.querySelector('button[type="submit"]');
        const buttonText = button.querySelector('.button-text');

        if (button && buttonText) {
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed');
            buttonText.textContent = 'Смена цели...';

            // Разблокируем кнопку через 2 секунды на случай ошибки
            setTimeout(() => {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                buttonText.textContent = 'Сменить цель';
            }, 2000);
        }

        return true; // Разрешаем отправку формы
    }
}
</script>
