<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Аванпосты - Echoes of Eternity</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg "
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar />
        </x-layout.hp-mp-bar>

        {{-- Валюты и прогресс опыта --}}
        <x-layout.currency-display :userProfile="$userProfile" />

        {{-- Приветственное сообщение --}}
        <x-battle.welcome-message />
        {{-- Хлебные крошки --}}
        <div class="">
            <x-breadcrumbs :breadcrumbs="$breadcrumbs" />
        </div>

        {{-- Декоративный разделитель --}}
        <div class="px-4 py-1">
            <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
        </div>

        {{-- Заголовок страницы --}}
        <div class="mb-2">
            @if (session('error'))
                <div
                    class="bg-[#613f36] text-[#ffeac1] p-1 rounded border border-[#88634a] shadow-md mb-3 {{ (isset($hasLowHp) && $hasLowHp) ? 'low-hp-warning' : '' }}">
                    {{ session('error') }}
                </div>
            @endif
            <x-layout.location-name :title="isset($pageTitle) ? $pageTitle : 'Аванпосты'" />
        </div>

        {{-- Блок выбора локаций --}}
        <x-battle.outposts.location-list :allLocations="$allLocations" :accessibleLocations="$accessibleLocations"
            :locationDeniedReasons="$locationDeniedReasons" :canEnterOutposts="$canEnterOutposts"
            :userLevel="$userLevel" />

        {{-- Кнопки навигации --}}
        @php
            $isStunned = Auth::user()->isStunned();
        @endphp
        <x-layout.navigation-buttons :isDisabled="$isStunned" />

        {{-- Футер --}}
        <x-layout.footer :onlineCount="$onlineCount" />

        {{-- Стили анимации для локаций --}}
        <x-battle.location-animations :hasLowHp="isset($hasLowHp) && $hasLowHp" />

        {{-- Модальное окно пролога --}}
        @if(isset($isPrologue) && $isPrologue)
            <x-prologue.outpost-modal />
        @endif

</body>

</html>