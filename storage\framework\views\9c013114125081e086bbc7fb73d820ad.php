<!DOCTYPE html>
<html lang="en">
<?php
use Illuminate\Support\Facades\Auth;
use App\Models\GuildInvitation;
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Echoes of Eternity - Главная'); ?></title>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js', 'resources/js/layout/server-time.js', 'resources/js/home/<USER>', 'resources/css/components/guild-invitation.css', 'resources/css/components/donation-button.css', 'resources/js/global/csrf.js', 'resources/js/global/notifications.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">


    
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">
        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0,'hasUnreadTicketComments' => $hasUnreadTicketComments ?? false,'unreadTicketCommentsCount' => $unreadTicketCommentsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0),'hasUnreadTicketComments' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadTicketComments ?? false),'unreadTicketCommentsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadTicketCommentsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginaleeccb55259fb1255815b60d773cef580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeccb55259fb1255815b60d773cef580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.home-location-image','data' => ['title' => 'Город','imagePath' => 'assets/UI/banner-border.png','imageAlt' => 'Город']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.home-location-image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Город','imagePath' => 'assets/UI/banner-border.png','imageAlt' => 'Город']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeccb55259fb1255815b60d773cef580)): ?>
<?php $attributes = $__attributesOriginaleeccb55259fb1255815b60d773cef580; ?>
<?php unset($__attributesOriginaleeccb55259fb1255815b60d773cef580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeccb55259fb1255815b60d773cef580)): ?>
<?php $component = $__componentOriginaleeccb55259fb1255815b60d773cef580; ?>
<?php unset($__componentOriginaleeccb55259fb1255815b60d773cef580); ?>
<?php endif; ?>

        
        <div class="text-center flex justify-center space-x-1 max-w-[300px] mx-auto">
            <?php if(session('welcome_message')): ?>
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    <?php echo e(session('welcome_message')); ?>

                </div>
            <?php endif; ?>
        </div>

        
        <?php if(Auth::check()): ?>
            <?php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            ?>
            <?php if (isset($component)) { $__componentOriginalf073577b00d5eaef97c81e52a3000637 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf073577b00d5eaef97c81e52a3000637 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.guild-invitation','data' => ['guildInvitation' => $guildInvitation]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.guild-invitation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['guildInvitation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($guildInvitation)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $attributes = $__attributesOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__attributesOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $component = $__componentOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__componentOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal172f669b41b59c7176f3664e320fd8b6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal172f669b41b59c7176f3664e320fd8b6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.announcement-notifications','data' => ['announcementData' => $announcementData ?? null,'unviewedAnnouncements' => $unviewedAnnouncements ?? collect()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.announcement-notifications'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['announcementData' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($announcementData ?? null),'unviewedAnnouncements' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unviewedAnnouncements ?? collect())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal172f669b41b59c7176f3664e320fd8b6)): ?>
<?php $attributes = $__attributesOriginal172f669b41b59c7176f3664e320fd8b6; ?>
<?php unset($__attributesOriginal172f669b41b59c7176f3664e320fd8b6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal172f669b41b59c7176f3664e320fd8b6)): ?>
<?php $component = $__componentOriginal172f669b41b59c7176f3664e320fd8b6; ?>
<?php unset($__componentOriginal172f669b41b59c7176f3664e320fd8b6); ?>
<?php endif; ?>



        
        <?php
            $navigationItems = [
                [
                    'route' => 'battle.index',
                    'title' => 'Сражение',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 17.5L3 6V3h3l11.5 11.5M14.5 17.5l3.5 3.5l-3-3l-3.5-3.5M14.5 17.5l3-3" /><path d="M13 6l-3 3" /></svg>'
                ],
                [
                    'route' => 'shop.index',
                    'title' => 'Торговля',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="9" cy="21" r="1" /><circle cx="20" cy="21" r="1" /><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6" /></svg>'
                ],
                [
                    'route' => 'masters.index',
                    'title' => 'Таверна',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 21v-6a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v6" /><path d="M19 21V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v16" /><path d="M3 7h18" /><path d="M5 21h14" /></svg>'
                ],
                [
                    'route' => 'professions.index',
                    'title' => 'Профессии',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 4.5c-1.5 0-2.5 1-2.5 2.5 0 1.5 1 2.5 2.5 2.5s2.5-1 2.5-2.5c0-1.5-1-2.5-2.5-2.5z" /><path d="M19.5 16.5L12 21l-7.5-4.5v-6L12 6l7.5 4.5v6z" /><path d="M12 12v9" /><path d="M7.5 9.5L12 12l4.5-2.5" /></svg>'
                ],
                [
                    'route' => 'farming.index',
                    'title' => 'Фермерство',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" /><path d="M8 10h8" /><path d="M12 6v8" /></svg>'
                ],
                [
                    'route' => 'square.index',
                    'title' => 'Площадь',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>'
                ],
                [
                    'route' => 'guilds.index',
                    'title' => 'Гильдии',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 21v-6a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v6" /><path d="M19 21V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v16" /><path d="M3 7h18" /><path d="M5 21h14" /></svg>'
                ],
                [
                    'route' => 'test-top',
                    'title' => 'Топ тестировщиков',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1Z"/></svg>'
                ],
                [
                    'route' => 'top-players.index',
                    'title' => 'Топ игроков',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 6l2 14H6l2-14" /><path d="M12 2v4" /><path d="M8 2h8" /><circle cx="12" cy="11" r="3" /></svg>'
                ]
            ];
        ?>

        <?php if (isset($component)) { $__componentOriginala7a60df6b103494b31185b1732b804db = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala7a60df6b103494b31185b1732b804db = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.main-navigation','data' => ['navigationItems' => $navigationItems,'isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.main-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['navigationItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($navigationItems),'isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala7a60df6b103494b31185b1732b804db)): ?>
<?php $attributes = $__attributesOriginala7a60df6b103494b31185b1732b804db; ?>
<?php unset($__attributesOriginala7a60df6b103494b31185b1732b804db); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala7a60df6b103494b31185b1732b804db)): ?>
<?php $component = $__componentOriginala7a60df6b103494b31185b1732b804db; ?>
<?php unset($__componentOriginala7a60df6b103494b31185b1732b804db); ?>
<?php endif; ?>

        
        <div class="mt-1">
            <?php if (isset($component)) { $__componentOriginalb5183a2c06ab1c06e76d28951184e93e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb5183a2c06ab1c06e76d28951184e93e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.donation-button','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.donation-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb5183a2c06ab1c06e76d28951184e93e)): ?>
<?php $attributes = $__attributesOriginalb5183a2c06ab1c06e76d28951184e93e; ?>
<?php unset($__attributesOriginalb5183a2c06ab1c06e76d28951184e93e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb5183a2c06ab1c06e76d28951184e93e)): ?>
<?php $component = $__componentOriginalb5183a2c06ab1c06e76d28951184e93e; ?>
<?php unset($__componentOriginalb5183a2c06ab1c06e76d28951184e93e); ?>
<?php endif; ?>
        </div>
    </div>

    
    <?php
        $isStunned = Auth::user()->isStunned();
    ?>
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => ['isDisabled' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['isDisabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>


</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/home.blade.php ENDPATH**/ ?>