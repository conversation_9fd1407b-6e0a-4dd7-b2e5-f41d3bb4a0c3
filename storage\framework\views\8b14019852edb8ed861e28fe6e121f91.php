

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['isStunned' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['isStunned' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Проверяем, заблокированы ли рудники для текущего пользователя
    $locationAccessService = app(\App\Services\LocationAccessService::class);
    $user = Auth::user();
    $isMinesBlocked = $user ? $locationAccessService->isLocationBlocked('Рудники', $user) : false;
?>

<div class="px-2 py-1">
    <div class="grid grid-cols-1 rounded-md overflow-hidden border border-[#a6925e] shadow-lg">
        
        <div class="bg-gradient-to-r from-[#3b3a33] to-[#4a4a3d] py-1 px-2 border-b border-[#a6925e]">
            <h3 class="text-center text-[#e5b769] font-semibold text-sm tracking-wider uppercase">Боевые локации
            </h3>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => ''.e(route('battle.outposts.index')).'','icon' => 'assets/iconOutpost.png','title' => 'Аванпосты','isActive' => true,'isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('battle.outposts.index')).'','icon' => 'assets/iconOutpost.png','title' => 'Аванпосты','isActive' => true,'isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => ''.e(route('battle.mines.index')).'','icon' => 'assets/iconMines.png','title' => 'Рудники','isActive' => !$isMinesBlocked,'isBlocked' => $isMinesBlocked,'isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('battle.mines.index')).'','icon' => 'assets/iconMines.png','title' => 'Рудники','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(!$isMinesBlocked),'isBlocked' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isMinesBlocked),'isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => ''.e(route('dungeons.index')).'','icon' => 'assets/iconDungeon.png','title' => 'Подземелья','isActive' => true,'isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('dungeons.index')).'','icon' => 'assets/iconDungeon.png','title' => 'Подземелья','isActive' => true,'isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <div class="px-2 py-0 bg-gradient-to-r from-[#292722] to-[#38352c]">
            <div class="h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50"></div>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => '#','icon' => 'assets/iconTrial.png','title' => 'Испытание','isActive' => false,'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'trial-tooltip\')','isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','icon' => 'assets/iconTrial.png','title' => 'Испытание','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'trial-tooltip\')','isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => '#','icon' => 'assets/iconDominions.png','title' => 'Доминионы','isActive' => false,'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'dominions-tooltip\')','isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','icon' => 'assets/iconDominions.png','title' => 'Доминионы','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'dominions-tooltip\')','isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => '#','icon' => 'assets/iconTemporary.png','title' => 'Временные событие','isActive' => false,'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'events-tooltip\')','isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','icon' => 'assets/iconTemporary.png','title' => 'Временные событие','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'events-tooltip\')','isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/location-menu.blade.php ENDPATH**/ ?>