@extends('layouts.mine')

@section('content')
    <!-- Статус фракций -->
    <div class="mt-2">
        <x-battle.faction-status
            :solWarriors="$solWarriors ?? []"
            :solMages="$solMages ?? []"
            :solKnights="$solKnights ?? []"
            :lunWarriors="$lunWarriors ?? []"
            :lunMages="$lunMages ?? []"
            :lunKnights="$lunKnights ?? []"
            :onlineCount="$onlineCount ?? 0"
        />
    </div>

    {{-- Блок ресурсов --}}
    <div class="mx-auto bg-[#2c2b25] text-white rounded-xl shadow-lg mb-4">
        <x-battle.mines.resource-block
            :resourcesInLocation="$resourcesInLocation ?? []"
            :user="$user ?? null"
            routePrefix="battle.mines.custom" />

    {{-- Блок мобов --}}
    <div class="bg-[#2f2d2b] mb-3 mx-auto p-1 max-w-sm text-[#d9d3b8]">
        <x-battle.mines.mob-list
            :mobsInLocation="$mobsInLocation ?? []"
            :user="$user ?? null"
            :isStunned="$isStunned ?? false"
            routePrefix="battle.mines.custom" />

    {{-- Блок действий с целью --}}
    <x-battle.mines.target-actions
        :user="$user ?? null"
        :targetResource="$targetResource ?? null"
        :targetMob="$targetMob ?? null"
        :targetBot="$targetBot ?? null"
        :target="$target ?? null"
        :isStunned="$isStunned ?? false"
        :lastAttacker="$lastAttacker ?? null"
        :lastAttackerResources="$lastAttackerResources ?? null"
        routePrefix="battle.mines.custom"
    />

@endsection