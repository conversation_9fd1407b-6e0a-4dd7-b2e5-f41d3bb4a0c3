<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 ТЕСТИРОВАНИЕ БЛОКИРОВКИ НАВИГАЦИИ ПРИ СТАНЕ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Получаем пользователя admin
$user = User::where('name', 'admin')->first();

if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "👤 Тестируем пользователя: {$user->name} (ID: {$user->id})\n\n";

// Проверяем текущее состояние стана
$isStunnedBefore = $user->isStunned();
echo "📊 Состояние стана до теста: " . ($isStunnedBefore ? "ОГЛУШЕН" : "НЕ ОГЛУШЕН") . "\n";

// Если уже оглушен, удаляем старые эффекты
if ($isStunnedBefore) {
    echo "🧹 Очищаем старые эффекты стана...\n";
    $user->activeEffects()->where('effect_type', 'stun')->delete();
}

echo "\n1️⃣ СОЗДАНИЕ ЭФФЕКТА СТАНА...\n";

// Создаем эффект стана на 60 секунд
$effect = ActiveEffect::create([
    'target_type' => 'App\\Models\\User',
    'target_id' => $user->id,
    'effect_type' => 'stun',
    'skill_id' => 14, // ID навыка "Тяжелый удар"
    'duration' => 60, // 60 секунд
    'starts_at' => now(),
    'ends_at' => now()->addSeconds(60),
    'effect_data' => [
        'message' => '⚡ Вы оглушены тяжелым ударом и не можете действовать!',
        'source' => 'test_system'
    ]
]);

echo "✅ Эффект стана создан (ID: {$effect->id})\n";
echo "⏰ Длительность: 60 секунд\n";
echo "🕐 Заканчивается: {$effect->ends_at}\n\n";

// Проверяем состояние после создания эффекта
$isStunnedAfter = $user->refresh()->isStunned();
echo "📊 Состояние стана после создания эффекта: " . ($isStunnedAfter ? "ОГЛУШЕН" : "НЕ ОГЛУШЕН") . "\n";

if (!$isStunnedAfter) {
    echo "❌ ОШИБКА: Эффект стана не применился!\n";
    exit(1);
}

echo "\n2️⃣ ТЕСТИРОВАНИЕ БЛОКИРОВКИ КОМПОНЕНТОВ...\n";

// Проверяем методы модели User
echo "🔍 Проверка методов User:\n";
echo "   - User->isStunned(): " . ($user->isStunned() ? '✅ ДА' : '❌ НЕТ') . "\n";
echo "   - User->canAttack(): " . ($user->canAttack() ? '❌ ДА (ОШИБКА!)' : '✅ НЕТ') . "\n";

// Проверяем метод isStunEffect у эффекта
echo "\n🔍 Проверка методов ActiveEffect:\n";
echo "   - Effect->isStunEffect(): " . ($effect->isStunEffect() ? '✅ ДА' : '❌ НЕТ') . "\n";
echo "   - Effect->isActive(): " . ($effect->isActive() ? '✅ ДА' : '❌ НЕТ') . "\n";

echo "\n3️⃣ ПРОВЕРКА БЛОКИРОВКИ НАВИГАЦИИ...\n";

$blockedRoutes = [
    'battle.outposts.index' => 'Аванпосты',
    'battle.mines.index' => 'Рудники', 
    'dungeons.index' => 'Подземелья',
    'shop.index' => 'Торговля',
    'party.index' => 'Группа',
    'guilds.index' => 'Гильдии',
    'inventory.index' => 'Рюкзак',
    'user.profile' => 'Персонаж'
];

echo "🚫 Следующие маршруты должны быть заблокированы:\n";
foreach ($blockedRoutes as $route => $name) {
    echo "   - {$name} ({$route})\n";
}

echo "\n4️⃣ ПРОВЕРКА ВИЗУАЛЬНЫХ ЭФФЕКТОВ...\n";

echo "🎨 Компоненты должны иметь стили при стане:\n";
echo "   - skills-panel: opacity-40 grayscale cursor-not-allowed pointer-events-none\n";
echo "   - target-actions: opacity-40 grayscale cursor-not-allowed pointer-events-none\n";
echo "   - quick-potion-bar: opacity-40 grayscale cursor-not-allowed pointer-events-none\n";
echo "   - main-navigation: opacity-40 grayscale cursor-not-allowed pointer-events-none\n";
echo "   - location-menu: opacity-40 grayscale cursor-not-allowed pointer-events-none\n";

echo "\n5️⃣ ПРОВЕРКА ДОСТУПНЫХ ДЕЙСТВИЙ...\n";

echo "✅ Следующие действия должны оставаться доступными:\n";
echo "   - Кнопка 'Обновить страницу' (🔄)\n";
echo "   - Кнопка 'Покинуть локацию/подземелье'\n";
echo "   - Просмотр информации (без действий)\n";

echo "\n6️⃣ ИНСТРУКЦИИ ДЛЯ РУЧНОГО ТЕСТИРОВАНИЯ...\n";

echo "🔧 Для проверки в браузере:\n";
echo "1. Откройте http://127.0.0.1:8000/ в браузере\n";
echo "2. Войдите как admin:qwe123\n";
echo "3. Перейдите в любую боевую локацию\n";
echo "4. Проверьте, что все навигационные элементы затемнены и некликабельны\n";
echo "5. Убедитесь, что доступна только кнопка 'Обновить страницу'\n";
echo "6. Попробуйте кликнуть на заблокированные элементы - ничего не должно происходить\n";

echo "\n⏱️ АВТОМАТИЧЕСКАЯ ОЧИСТКА...\n";
echo "Эффект стана автоматически закончится через 60 секунд.\n";
echo "Или используйте команду очистки:\n";
echo "php artisan tinker --execute=\"App\\Models\\User::where('name', 'admin')->first()->activeEffects()->where('effect_type', 'stun')->delete(); echo 'Эффекты стана очищены';\"\n";

echo "\n✅ ТЕСТ ЗАВЕРШЕН!\n";
echo "Эффект стана активен. Проверьте блокировку в браузере.\n";
