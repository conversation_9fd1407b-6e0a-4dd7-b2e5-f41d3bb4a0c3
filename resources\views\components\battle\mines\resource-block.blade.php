@props(['resourcesInLocation' => [], 'user' => null, 'routePrefix' => 'battle.mines.custom', 'isStunned' => false])

{{--
Компонент для отображения блока ресурсов в локациях рудника
Принимает:
- resourcesInLocation: коллекция ресурсов в текущей локации
- user: текущий пользователь
- routePrefix: префикс маршрута для действий
- isStunned: флаг оглушения игрока
--}}

<div class="flex justify-center items-center py-0 px-2 gap-1.5">
    @forelse ($resourcesInLocation as $res)
        @php
            // Проверяем, выбран ли данный ресурс
            $isSelected = $user->current_target_id == $res->id && $user->current_target_type == 'resource';
        @endphp
        <form action="{{ route($routePrefix . '.select-resource', [request()->route('slug'), $res->id]) }}" method="POST"
            class="flex-shrink-0">
            @csrf
            <button type="submit" {{ $isStunned ? 'disabled' : '' }} class="relative w-11 h-11 bg-gradient-to-b from-[#5a4d36] to-[#453b2a]
                                    border-2 {{ $isSelected ? 'border-[#e9d5a0] resource-glow' : 'border-[#a6925e]' }}
                                    {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:brightness-125 hover:shadow-[0_0_5px_#a6925e70]' }}
                                    transition-all duration-300 rounded-md overflow-hidden">

                {{-- Иконка ресурса --}}
                <img src="{{ $res->resource->icon_path }}" alt="{{ $res->resource->name ?? 'Ресурс' }}"
                    class="w-full h-full object-cover p-0.5 {{ $isSelected ? 'animate-pulse' : '' }}"
                    onerror="this.src='{{ asset('assets/resources/EtherealOrb.png') }}'">

                {{-- Индикатор прочности --}}
                @if (isset($res->durability))
                    @php
                        // Определяем цвет в зависимости от прочности
                        $durabilityColor =
                            $res->durability > 70
                            ? '#4CAF50'
                            : ($res->durability > 30
                                ? '#FFC107'
                                : '#F44336');
                    @endphp

                    {{-- Полоса прочности внизу ресурса --}}
                    <div class="absolute bottom-0 left-0 right-0 h-1.5 bg-[#1a1915] opacity-70">
                        <div class="h-full {{ $isSelected ? 'animate-pulse' : '' }}"
                            style="width: {{ $res->durability }}%; background-color: {{ $durabilityColor }}">
                        </div>
                    </div>
                @endif
            </button>
        </form>
    @empty
        <div class="text-center text-[#a6925e] text-sm py-2">
            Ресурсы отсутствуют в этой локации.
        </div>
    @endforelse
</div>

<style>
    @keyframes resourcePulse {
        0% {
            box-shadow: 0 0 2px 1px rgba(233, 213, 160, 0.3);
        }

        50% {
            box-shadow: 0 0 8px 3px rgba(233, 213, 160, 0.7);
        }

        100% {
            box-shadow: 0 0 2px 1px rgba(233, 213, 160, 0.3);
        }
    }

    .resource-glow {
        animation: resourcePulse 2s infinite ease-in-out;
    }
</style>