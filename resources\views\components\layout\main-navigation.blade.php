@props(['navigationItems' => [], 'isStunned' => false])

{{--
Компонент основной навигации
Отображает список навигационных кнопок в стиле RPG с адаптивными размерами
Параметры:
- navigationItems: массив элементов навигации с ключами:
- route: маршрут для ссылки
- title: заголовок кнопки
- icon: не используется (заменено на фоновые изображения)
- iconType: не используется (заменено на фоновые изображения)
Примечание: Каждая кнопка использует свое изображение с текстом:
- "Сражение" -> button-link-battle.png
- "Торговля" -> button-link-shop.png
- Остальные -> linkUI.png
Оптимизации: адаптивные размеры, улучшенное качество изображений на всех экранах,
оптимизированная производительность с Events/Listeners
--}}

<div class="w-full navigation-container" style="margin: 0; padding: 0; line-height: 0; font-size: 0;">
    @foreach($navigationItems as $item)
        <a href="{{ $isStunned ? '#' : route($item['route']) }}"
            class="navigation-button-link block w-full max-w-full {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed pointer-events-none' : '' }}"
            style="margin: 0; padding: 0; line-height: 0; font-size: 0; display: block;" data-route="{{ $item['route'] }}"
            data-title="{{ $item['title'] }}" {{ $isStunned ? 'onclick="event.preventDefault(); return false;"' : '' }}>

            {{-- Адаптивное изображение кнопки --}}
            @php
                // Определяем изображение в зависимости от типа кнопки
                $buttonImage = match ($item['title']) {
                    'Сражение' => 'assets/UI/button-link-battle.png',
                    'Торговля' => 'assets/UI/button-link-shop.png',
                    'Таверна' => 'assets/UI/button-link-tavern.png',
                    'Профессии' => 'assets/UI/button-link-proff.png',
                    'Фермерство' => 'assets/UI/button-link-farm.png',
                    'Площадь' => 'assets/UI/button-link-square.png',
                    'Гильдии' => 'assets/UI/button-link-guild.png',
                    'Топ игроков' => 'assets/UI/top-players-nav.png',
                    'Топ тестировщиков' => 'assets/UI/top-test-nav.png',
                    default => 'assets/UI/linkUI.png'
                };
            @endphp
            <img src="{{ asset($buttonImage) }}" alt="{{ $item['title'] }}" class="navigation-button-image w-full block"
                style="margin: 0; padding: 0; display: block; vertical-align: top; line-height: 0; font-size: 0; border: none; outline: none;"
                loading="eager">

        </a>
    @endforeach
</div>