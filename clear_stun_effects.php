<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧹 ОЧИСТКА ЭФФЕКТОВ СТАНА\n";
echo "=" . str_repeat("=", 30) . "\n\n";

// Получаем пользователя admin
$user = User::where('name', 'admin')->first();

if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "👤 Пользователь: {$user->name} (ID: {$user->id})\n";

// Проверяем текущее состояние стана
$isStunnedBefore = $user->isStunned();
echo "📊 Состояние стана до очистки: " . ($isStunnedBefore ? "ОГЛУШЕН" : "НЕ ОГЛУШЕН") . "\n\n";

if (!$isStunnedBefore) {
    echo "ℹ️ Пользователь не оглушен. Очистка не требуется.\n";
    exit(0);
}

// Получаем все эффекты стана
$stunEffects = $user->activeEffects()->where('effect_type', 'stun')->get();
echo "🔍 Найдено эффектов стана: {$stunEffects->count()}\n";

if ($stunEffects->count() > 0) {
    foreach ($stunEffects as $effect) {
        echo "   - Эффект ID: {$effect->id}, Заканчивается: {$effect->ends_at}\n";
    }
    
    // Удаляем все эффекты стана
    $deletedCount = $user->activeEffects()->where('effect_type', 'stun')->delete();
    echo "\n✅ Удалено эффектов стана: {$deletedCount}\n";
} else {
    echo "ℹ️ Эффекты стана не найдены.\n";
}

// Проверяем состояние после очистки
$isStunnedAfter = $user->refresh()->isStunned();
echo "📊 Состояние стана после очистки: " . ($isStunnedAfter ? "ОГЛУШЕН" : "НЕ ОГЛУШЕН") . "\n";

if ($isStunnedAfter) {
    echo "⚠️ ВНИМАНИЕ: Пользователь все еще оглушен! Возможно есть другие эффекты стана.\n";
    
    // Проверяем все активные эффекты
    $allEffects = $user->activeEffects()->get();
    echo "🔍 Все активные эффекты:\n";
    foreach ($allEffects as $effect) {
        $isStun = $effect->isStunEffect() ? ' (СТАН)' : '';
        echo "   - ID: {$effect->id}, Тип: {$effect->effect_type}, Skill ID: {$effect->skill_id}{$isStun}\n";
    }
} else {
    echo "✅ Стан успешно снят!\n";
}

echo "\n🔧 Теперь можно проверить в браузере, что навигация разблокирована.\n";
