@props(['mobsInLocation' => [], 'user' => null, 'routePrefix' => 'battle.mines.custom', 'isStunned' => false])

{{--
Компонент для отображения списка мобов в локациях рудника
Принимает:
- mobsInLocation: коллекция мобов в текущей локации
- user: текущий пользователь
- routePrefix: префикс маршрута для действий
- isStunned: флаг оглушения игрока
--}}

<div class="mt-2 m-0 p-0 relative z-10">
    <div class="grid grid-cols-3 gap-2 justify-items-center">
        @forelse ($mobsInLocation as $mob)
            @php
                $isSelected = $user->current_target_id == $mob->id && $user->current_target_type == 'mob';
                $hpPercent = (($mob->current_hp ?? $mob->hp) / ($mob->max_hp ?? $mob->hp)) * 100;

                // Динамическое изменение цвета от зеленого к красному
                if ($hpPercent > 80) {
                    $hpColor = '#22C55E'; // Зеленый
                    $hpGlow = 'rgba(34, 197, 94, 0.6)';
                } elseif ($hpPercent > 60) {
                    $hpColor = '#84CC16'; // Лайм-зеленый
                    $hpGlow = 'rgba(132, 204, 22, 0.6)';
                } elseif ($hpPercent > 40) {
                    $hpColor = '#EAB308'; // Желтый
                    $hpGlow = 'rgba(234, 179, 8, 0.6)';
                } elseif ($hpPercent > 25) {
                    $hpColor = '#F97316'; // Оранжевый
                    $hpGlow = 'rgba(249, 115, 22, 0.7)';
                } elseif ($hpPercent > 10) {
                    $hpColor = '#EF4444'; // Красный
                    $hpGlow = 'rgba(239, 68, 68, 0.8)';
                } else {
                    $hpColor = '#DC2626'; // Темно-красный
                    $hpGlow = 'rgba(220, 38, 38, 0.9)';
                }
            @endphp
            <form action="{{ route($routePrefix . '.select-mob', [request()->route('slug'), $mob->id]) }}" method="POST"
                class="m-0 p-0" style="margin: 0; padding: 0;">
                @csrf
                <button type="submit" {{ $isStunned ? 'disabled' : '' }} class="w-20 h-24 m-0 p-1 relative transform transition-all duration-200
                        {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed' : 'hover:scale-105' }}
                        {{ $isSelected ? 'mob-card-selected' : 'mob-card-default' }}" style="margin: 0; padding: 2px;">

                    {{-- Фоновая рамка карточки моба --}}
                    <div
                        class="absolute inset-0 rounded-lg bg-gradient-to-b from-[#3b3629] to-[#2a2722] border {{ $isSelected ? 'border-[#c1a96e] shadow-glow' : 'border-[#6e3f35]/60' }}">
                    </div>

                    <div class="relative z-10 flex flex-col items-center h-full justify-between py-1">
                        {{-- Иконка моба --}}
                        <div class="relative mb-1">
                            <img src="{{ asset($mob->icon ?? 'assets/wolfIcon.png') }}" alt="{{ $mob->name }}"
                                class="w-12 h-12 rounded-full {{ $isSelected ? 'shadow-icon-glow' : '' }} border border-[#3b3629]">

                            {{-- Индикатор уровня моба --}}
                            <div
                                class="absolute -bottom-1 -right-1 bg-gradient-to-b from-[#6e3f35] to-[#59372d] text-[#f8eac2] text-[8px] rounded-full w-3.5 h-3.5 flex items-center justify-center border border-[#c1a96e] shadow-sm">
                                {{ $mob->level ?? '?' }}
                            </div>
                        </div>

                        {{-- Активные эффекты --}}
                        <div class="w-full mb-1">
                            <x-battle.mob-active-effects :mob="$mob" :maxEffects="3" />
                        </div>

                        {{-- Полоса здоровья с магическим эффектом --}}
                        <div class="w-full h-3 bg-[#1a1814] rounded-sm relative border border-[#3b3629] overflow-hidden">
                            {{-- Фоновая текстура --}}
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#2a2722]/20 to-transparent">
                            </div>

                            {{-- Основная полоса HP --}}
                            <div class="h-full rounded-sm transition-all duration-300 relative"
                                style="width: {{ $hpPercent }}%; background: linear-gradient(to right, {{ $hpColor }}, {{ $hpColor }}CC); box-shadow: 0 0 4px {{ $hpGlow }};">
                                {{-- Внутреннее свечение --}}
                                <div class="absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-transparent">
                                </div>
                            </div>

                            {{-- Значение HP --}}
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span
                                    class="text-[#f8eac2] text-[9px] font-bold drop-shadow-[0_0_2px_rgba(0,0,0,1)]">{{ $mob->current_hp ?? $mob->hp }}</span>
                            </div>
                        </div>
                    </div>
                </button>
            </form>
        @empty
            <div class="col-span-3 text-center py-4">
                <div class="bg-gradient-to-b from-[#3b3629] to-[#2a2722] border border-[#6e3f35]/60 rounded-lg p-3">
                    <p class="text-[#998d66] text-sm">Монстры отсутствуют в этой локации</p>
                </div>
            </div>
        @endforelse
    </div>
</div>

<style>
    /* Анимация золотого свечения для выбранного моба */
    @keyframes goldenGlow {
        0% {
            box-shadow: 0 0 8px rgba(193, 169, 110, 0.4), inset 0 0 8px rgba(193, 169, 110, 0.1);
        }

        50% {
            box-shadow: 0 0 16px rgba(193, 169, 110, 0.7), inset 0 0 12px rgba(193, 169, 110, 0.2);
        }

        100% {
            box-shadow: 0 0 8px rgba(193, 169, 110, 0.4), inset 0 0 8px rgba(193, 169, 110, 0.1);
        }
    }

    /* Анимация для иконки выбранного моба */
    @keyframes iconMysticGlow {
        0% {
            box-shadow: 0 0 4px rgba(193, 169, 110, 0.5);
        }

        50% {
            box-shadow: 0 0 8px rgba(193, 169, 110, 0.8), 0 0 12px rgba(193, 169, 110, 0.3);
        }

        100% {
            box-shadow: 0 0 4px rgba(193, 169, 110, 0.5);
        }
    }

    /* Базовые стили для карточек мобов */
    .mob-card-default {
        transition: all 0.3s ease;
    }

    .mob-card-default:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .mob-card-selected {
        animation: goldenGlow 2.5s infinite ease-in-out;
    }

    .shadow-glow {
        box-shadow: 0 0 12px rgba(193, 169, 110, 0.6);
    }

    .shadow-icon-glow {
        animation: iconMysticGlow 2.5s infinite ease-in-out;
    }

    /* Улучшенные тени для текста */
    .text-shadow-strong {
        text-shadow: 0 0 3px rgba(0, 0, 0, 0.8), 0 0 6px rgba(0, 0, 0, 0.6);
    }
</style>