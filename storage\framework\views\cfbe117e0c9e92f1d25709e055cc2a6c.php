<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Аванпосты - Echoes of Eternity</title>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg "
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal330dedb9455611dafc44712602451f6f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal330dedb9455611dafc44712602451f6f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.welcome-message','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.welcome-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal330dedb9455611dafc44712602451f6f)): ?>
<?php $attributes = $__attributesOriginal330dedb9455611dafc44712602451f6f; ?>
<?php unset($__attributesOriginal330dedb9455611dafc44712602451f6f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal330dedb9455611dafc44712602451f6f)): ?>
<?php $component = $__componentOriginal330dedb9455611dafc44712602451f6f; ?>
<?php unset($__componentOriginal330dedb9455611dafc44712602451f6f); ?>
<?php endif; ?>
        
        <div class="">
            <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
        </div>

        
        <div class="px-4 py-1">
            <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
        </div>

        
        <div class="mb-2">
            <?php if(session('error')): ?>
                <div
                    class="bg-[#613f36] text-[#ffeac1] p-1 rounded border border-[#88634a] shadow-md mb-3 <?php echo e((isset($hasLowHp) && $hasLowHp) ? 'low-hp-warning' : ''); ?>">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalc249f68c23236e1f9e731ca34e4bd461 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc249f68c23236e1f9e731ca34e4bd461 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-name','data' => ['title' => isset($pageTitle) ? $pageTitle : 'Аванпосты']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-name'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(isset($pageTitle) ? $pageTitle : 'Аванпосты')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc249f68c23236e1f9e731ca34e4bd461)): ?>
<?php $attributes = $__attributesOriginalc249f68c23236e1f9e731ca34e4bd461; ?>
<?php unset($__attributesOriginalc249f68c23236e1f9e731ca34e4bd461); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc249f68c23236e1f9e731ca34e4bd461)): ?>
<?php $component = $__componentOriginalc249f68c23236e1f9e731ca34e4bd461; ?>
<?php unset($__componentOriginalc249f68c23236e1f9e731ca34e4bd461); ?>
<?php endif; ?>
        </div>

        
        <?php if (isset($component)) { $__componentOriginalea9e9501bfea6b243236b38125094fbe = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalea9e9501bfea6b243236b38125094fbe = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.outposts.location-list','data' => ['allLocations' => $allLocations,'accessibleLocations' => $accessibleLocations,'locationDeniedReasons' => $locationDeniedReasons,'canEnterOutposts' => $canEnterOutposts,'userLevel' => $userLevel]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.outposts.location-list'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['allLocations' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allLocations),'accessibleLocations' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($accessibleLocations),'locationDeniedReasons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($locationDeniedReasons),'canEnterOutposts' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($canEnterOutposts),'userLevel' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userLevel)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalea9e9501bfea6b243236b38125094fbe)): ?>
<?php $attributes = $__attributesOriginalea9e9501bfea6b243236b38125094fbe; ?>
<?php unset($__attributesOriginalea9e9501bfea6b243236b38125094fbe); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalea9e9501bfea6b243236b38125094fbe)): ?>
<?php $component = $__componentOriginalea9e9501bfea6b243236b38125094fbe; ?>
<?php unset($__componentOriginalea9e9501bfea6b243236b38125094fbe); ?>
<?php endif; ?>

        
        <?php
            $isStunned = Auth::user()->isStunned();
        ?>
        <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => ['isDisabled' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['isDisabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal2930f16274775292a4c029bfece08059 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2930f16274775292a4c029bfece08059 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-animations','data' => ['hasLowHp' => isset($hasLowHp) && $hasLowHp]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-animations'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasLowHp' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(isset($hasLowHp) && $hasLowHp)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2930f16274775292a4c029bfece08059)): ?>
<?php $attributes = $__attributesOriginal2930f16274775292a4c029bfece08059; ?>
<?php unset($__attributesOriginal2930f16274775292a4c029bfece08059); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2930f16274775292a4c029bfece08059)): ?>
<?php $component = $__componentOriginal2930f16274775292a4c029bfece08059; ?>
<?php unset($__componentOriginal2930f16274775292a4c029bfece08059); ?>
<?php endif; ?>

        
        <?php if(isset($isPrologue) && $isPrologue): ?>
            <?php if (isset($component)) { $__componentOriginal9caae3d2c219ec1260ba273135136cfa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9caae3d2c219ec1260ba273135136cfa = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.prologue.outpost-modal','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('prologue.outpost-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9caae3d2c219ec1260ba273135136cfa)): ?>
<?php $attributes = $__attributesOriginal9caae3d2c219ec1260ba273135136cfa; ?>
<?php unset($__attributesOriginal9caae3d2c219ec1260ba273135136cfa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9caae3d2c219ec1260ba273135136cfa)): ?>
<?php $component = $__componentOriginal9caae3d2c219ec1260ba273135136cfa; ?>
<?php unset($__componentOriginal9caae3d2c219ec1260ba273135136cfa); ?>
<?php endif; ?>
        <?php endif; ?>

</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/battle/outposts.blade.php ENDPATH**/ ?>